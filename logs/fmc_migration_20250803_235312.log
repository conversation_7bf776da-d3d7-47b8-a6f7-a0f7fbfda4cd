2025-08-03 23:53:12 | INFO     | setup_logging        | ================================================================================
2025-08-03 23:53:12 | INFO     | setup_logging        | FMC Migration Session Started
2025-08-03 23:53:12 | INFO     | setup_logging        | Timestamp: 2025-08-03 23:53:12
2025-08-03 23:53:12 | INFO     | setup_logging        | FMC Host: https://*************
2025-08-03 23:53:12 | INFO     | setup_logging        | Overwrite Mode: False
2025-08-03 23:53:12 | INFO     | setup_logging        | Log Files Created:
2025-08-03 23:53:12 | INFO     | setup_logging        |   - Main: logs/fmc_migration_20250803_235312.log
2025-08-03 23:53:12 | INFO     | setup_logging        |   - Errors: logs/fmc_errors_20250803_235312.log
2025-08-03 23:53:12 | INFO     | setup_logging        |   - Corrections: logs/fmc_format_corrections_20250803_235312.log
2025-08-03 23:53:12 | INFO     | setup_logging        |   - Debug: logs/fmc_debug_20250803_235312.log
2025-08-03 23:53:12 | INFO     | setup_logging        | ================================================================================
2025-08-03 23:53:12 | INFO     | log                  | Validating migration configuration...
2025-08-03 23:53:12 | INFO     | log                  | Running offline validation (skipping FMC connectivity check)...
2025-08-03 23:53:12 | INFO     | log                  | Validating data formats in migration configuration...
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'obj-tcp-eq-15002':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '15002' → '15002'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'obj-tcp-eq-15331':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '15331' → '15331'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'obj-tcp-eq-3389':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '3389' → '3389'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'obj-tcp-eq-2222':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '2222' → '2222'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'obj-tcp-eq-6544':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '6544' → '6544'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'obj-tcp-eq-2020':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '2020' → '2020'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'obj-tcp-eq-15031':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '15031' → '15031'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'obj-udp-eq-15032':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '15032' → '15032'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'obj-tcp-eq-55443':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '55443' → '55443'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'obj-tcp-eq-3401':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '3401' → '3401'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'obj-tcp-eq-53048':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '53048' → '53048'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'obj-tcp-eq-53372':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '53372' → '53372'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'obj-tcp-eq-53050':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '53050' → '53050'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'obj-tcp-eq-53374':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '53374' → '53374'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'NLI-BG13-FTP':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '1433' → '1433'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'W32.MYDOOM.OLD':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '3127' → '3127'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'GREYCASTLE_VPN':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '51820' → '51820'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'IMO_CLOUD':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '42045' → '42045'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'NOVA-8070-TCP':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '8070' → '8070'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'REYHEALTH.EXTERNAL.PORT1':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '18009' → '18009'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'REYHEALTH.EXTERNAL.PORT2':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '18005' → '18005'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
2025-08-03 23:53:12 | INFO     | log                  | Applied automatic format corrections to 'NOVA.TOPAZ':
2025-08-03 23:53:12 | INFO     | log                  |   - port: '47290' → '47290'
2025-08-03 23:53:12 | INFO     | log                  |     Reason: Port converted to integer
