2025-08-01 13:27:53 | INFO     | setup_logging        | ================================================================================
2025-08-01 13:27:53 | INFO     | setup_logging        | FMC Migration Session Started
2025-08-01 13:27:53 | INFO     | setup_logging        | Timestamp: 2025-08-01 13:27:53
2025-08-01 13:27:53 | INFO     | setup_logging        | FMC Host: https://*************
2025-08-01 13:27:53 | INFO     | setup_logging        | Overwrite Mode: False
2025-08-01 13:27:53 | INFO     | setup_logging        | Log Files Created:
2025-08-01 13:27:53 | INFO     | setup_logging        |   - Main: logs/fmc_migration_20250801_132753.log
2025-08-01 13:27:53 | INFO     | setup_logging        |   - Errors: logs/fmc_errors_20250801_132753.log
2025-08-01 13:27:53 | INFO     | setup_logging        |   - Corrections: logs/fmc_format_corrections_20250801_132753.log
2025-08-01 13:27:53 | INFO     | setup_logging        |   - Debug: logs/fmc_debug_20250801_132753.log
2025-08-01 13:27:53 | INFO     | setup_logging        | ================================================================================
2025-08-01 13:27:53 | INFO     | log                  | Validating migration configuration...
2025-08-01 13:27:53 | INFO     | log                  | Retrieving existing objects for validation...
2025-08-01 13:27:53 | INFO     | log                  | Authenticating with FMC...
2025-08-01 13:28:23 | ERROR    | log                  | Authentication error: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x106fd6e40>, 'Connection to ************* timed out. (connect timeout=30)'))
2025-08-01 13:28:23 | INFO     | log                  | Validating data formats in migration configuration...
