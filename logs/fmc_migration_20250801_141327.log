2025-08-01 14:13:27 | INFO     | setup_logging        | ================================================================================
2025-08-01 14:13:27 | INFO     | setup_logging        | FMC Migration Session Started
2025-08-01 14:13:27 | INFO     | setup_logging        | Timestamp: 2025-08-01 14:13:27
2025-08-01 14:13:27 | INFO     | setup_logging        | FMC Host: https://*************
2025-08-01 14:13:27 | INFO     | setup_logging        | Overwrite Mode: True
2025-08-01 14:13:27 | INFO     | setup_logging        | Log Files Created:
2025-08-01 14:13:27 | INFO     | setup_logging        |   - Main: logs/fmc_migration_20250801_141327.log
2025-08-01 14:13:27 | INFO     | setup_logging        |   - Errors: logs/fmc_errors_20250801_141327.log
2025-08-01 14:13:27 | INFO     | setup_logging        |   - Corrections: logs/fmc_format_corrections_20250801_141327.log
2025-08-01 14:13:27 | INFO     | setup_logging        |   - Debug: logs/fmc_debug_20250801_141327.log
2025-08-01 14:13:27 | INFO     | setup_logging        | ================================================================================
2025-08-01 14:13:27 | INFO     | log                  | Loading migration configuration from fmc_migration_config.json...
2025-08-01 14:13:27 | INFO     | log                  | Authenticating with FMC...
2025-08-01 14:13:57 | ERROR    | log                  | Authentication error: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x106bfaf90>, 'Connection to ************* timed out. (connect timeout=30)'))
2025-08-01 14:13:57 | ERROR    | log                  | Failed to authenticate with FMC
