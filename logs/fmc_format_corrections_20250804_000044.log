2025-08-04 00:00:44 | INFO     | Format corrections applied to 'obj-tcp-eq-15002':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 15002
2025-08-04 00:00:44 | INFO     |   Corrected: 15002
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'obj-tcp-eq-15331':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 15331
2025-08-04 00:00:44 | INFO     |   Corrected: 15331
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'obj-tcp-eq-3389':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 3389
2025-08-04 00:00:44 | INFO     |   Corrected: 3389
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'obj-tcp-eq-2222':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 2222
2025-08-04 00:00:44 | INFO     |   Corrected: 2222
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'obj-tcp-eq-6544':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 6544
2025-08-04 00:00:44 | INFO     |   Corrected: 6544
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'obj-tcp-eq-2020':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 2020
2025-08-04 00:00:44 | INFO     |   Corrected: 2020
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'obj-tcp-eq-15031':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 15031
2025-08-04 00:00:44 | INFO     |   Corrected: 15031
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'obj-udp-eq-15032':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 15032
2025-08-04 00:00:44 | INFO     |   Corrected: 15032
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'obj-tcp-eq-55443':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 55443
2025-08-04 00:00:44 | INFO     |   Corrected: 55443
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'obj-tcp-eq-3401':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 3401
2025-08-04 00:00:44 | INFO     |   Corrected: 3401
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'obj-tcp-eq-53048':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 53048
2025-08-04 00:00:44 | INFO     |   Corrected: 53048
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'obj-tcp-eq-53372':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 53372
2025-08-04 00:00:44 | INFO     |   Corrected: 53372
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'obj-tcp-eq-53050':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 53050
2025-08-04 00:00:44 | INFO     |   Corrected: 53050
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'obj-tcp-eq-53374':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 53374
2025-08-04 00:00:44 | INFO     |   Corrected: 53374
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'NLI-BG13-FTP':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 1433
2025-08-04 00:00:44 | INFO     |   Corrected: 1433
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'W32.MYDOOM.OLD':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 3127
2025-08-04 00:00:44 | INFO     |   Corrected: 3127
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'GREYCASTLE_VPN':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 51820
2025-08-04 00:00:44 | INFO     |   Corrected: 51820
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'IMO_CLOUD':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 42045
2025-08-04 00:00:44 | INFO     |   Corrected: 42045
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'NOVA-8070-TCP':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 8070
2025-08-04 00:00:44 | INFO     |   Corrected: 8070
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'REYHEALTH.EXTERNAL.PORT1':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 18009
2025-08-04 00:00:44 | INFO     |   Corrected: 18009
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'REYHEALTH.EXTERNAL.PORT2':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 18005
2025-08-04 00:00:44 | INFO     |   Corrected: 18005
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
2025-08-04 00:00:44 | INFO     | Format corrections applied to 'NOVA.TOPAZ':
2025-08-04 00:00:44 | INFO     |   Field: port
2025-08-04 00:00:44 | INFO     |   Original: 47290
2025-08-04 00:00:44 | INFO     |   Corrected: 47290
2025-08-04 00:00:44 | INFO     |   Reason: Port converted to integer
2025-08-04 00:00:44 | INFO     |   --------------------------------------------------
