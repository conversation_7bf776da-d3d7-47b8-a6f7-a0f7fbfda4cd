2025-08-04 02:11:17,313 | INFO | ================================================================================
2025-08-04 02:11:17,313 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 02:11:17,313 | INFO | Session ID: migration_1754298677
2025-08-04 02:11:17,313 | INFO | Connection Type: custom
2025-08-04 02:11:17,313 | INFO | 🔍 Connection Diagnostic:
2025-08-04 02:11:17,313 | INFO |    • FMC Object Type: <class 'fmc_api_executor.FMCAPIExecutor'>
2025-08-04 02:11:17,313 | INFO |    • fmcapi Available: True
2025-08-04 02:11:17,313 | INFO |    • Available Methods: _live_lookup_object_by_name, create_object_with_retry
2025-08-04 02:11:17,313 | INFO | 🔍 Connection diagnostic complete
2025-08-04 02:11:17,313 | INFO | ================================================================================
2025-08-04 02:11:17,313 | INFO | [LOAD] Loading migration configuration: fmc_migration_config.json
2025-08-04 02:11:17,316 | INFO | [DOC] Detected v1.0 config format (api_calls structure)
2025-08-04 02:11:17,316 | INFO | [INFO] Found 629 host objects in v1.0 format
2025-08-04 02:11:17,316 | INFO | [INFO] Found 63 network objects in v1.0 format
2025-08-04 02:11:17,316 | INFO | [INFO] Found 29 service objects in v1.0 format
2025-08-04 02:11:17,316 | INFO | [START] Starting Host Objects migration...
2025-08-04 02:11:17,316 | INFO | [INFO] Processing 629 hosts objects...
2025-08-04 02:11:17,316 | DEBUG | Processing hosts 1: RadSaratoga
2025-08-04 02:11:17,316 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 02:11:17,316 | DEBUG | Object data: {'name': 'RadSaratoga', 'type': 'Host', 'value': '***********', 'description': 'Migrated from ASA - ', 'overridable': False}
