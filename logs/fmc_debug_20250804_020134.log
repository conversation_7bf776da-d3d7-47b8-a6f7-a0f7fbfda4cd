2025-08-04 02:01:34 | INFO     | setup_logging        | ================================================================================
2025-08-04 02:01:34 | INFO     | setup_logging        | FMC Migration Session Started
2025-08-04 02:01:34 | INFO     | setup_logging        | Timestamp: 2025-08-04 02:01:34
2025-08-04 02:01:34 | INFO     | setup_logging        | FMC Host: https://*************
2025-08-04 02:01:34 | INFO     | setup_logging        | Overwrite Mode: True
2025-08-04 02:01:34 | INFO     | setup_logging        | Log Files Created:
2025-08-04 02:01:34 | INFO     | setup_logging        |   - Main: logs/fmc_migration_20250804_020134.log
2025-08-04 02:01:34 | INFO     | setup_logging        |   - Errors: logs/fmc_errors_20250804_020134.log
2025-08-04 02:01:34 | INFO     | setup_logging        |   - Corrections: logs/fmc_format_corrections_20250804_020134.log
2025-08-04 02:01:34 | INFO     | setup_logging        |   - Debug: logs/fmc_debug_20250804_020134.log
2025-08-04 02:01:34 | INFO     | setup_logging        | ================================================================================
2025-08-04 02:01:34 | DEBUG    | log                  | Cached lookup result for object/hosts:radsaratoga
2025-08-04 02:01:34 | INFO     | log                  | Authenticating with FMC...
2025-08-04 02:02:04 | ERROR    | log                  | Authentication error: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x1038082f0>, 'Connection to ************* timed out. (connect timeout=30)'))
2025-08-04 02:02:04 | DEBUG    | log                  | 🎯 GUARANTEED SUCCESS MODE: Creating object/hosts 'RadSaratoga' with up to 10 retries
2025-08-04 02:02:04 | DEBUG    | log                  | Object 'RadSaratoga' of type 'object/hosts' not found in cache (skipping live lookup during bulk operations)
2025-08-04 02:02:04 | DEBUG    | log                  | Cache has 0 unknown objects. Sample names: []
2025-08-04 02:02:04 | DEBUG    | log                  | No existing object/hosts 'RadSaratoga' found in cache - will attempt creation
2025-08-04 02:02:04 | ERROR    | log                  | Unknown object type for creation: object/hosts
2025-08-04 02:02:04 | WARNING  | log                  | 🔄 Attempt 1/10 failed for 'RadSaratoga': Failed to create object/hosts: RadSaratoga
2025-08-04 02:02:04 | INFO     | log                  | ⏳ Waiting 2s before retry 2/10 for RadSaratoga
2025-08-04 02:02:06 | DEBUG    | log                  | Object 'RadSaratoga' of type 'object/hosts' not found in cache (skipping live lookup during bulk operations)
2025-08-04 02:02:06 | DEBUG    | log                  | Cache has 0 unknown objects. Sample names: []
2025-08-04 02:02:06 | DEBUG    | log                  | No existing object/hosts 'RadSaratoga' found in cache - will attempt creation
2025-08-04 02:02:06 | ERROR    | log                  | Unknown object type for creation: object/hosts
2025-08-04 02:02:06 | WARNING  | log                  | 🔄 Attempt 2/10 failed for 'RadSaratoga': Failed to create object/hosts: RadSaratoga
2025-08-04 02:02:06 | INFO     | log                  | ⏳ Waiting 4s before retry 3/10 for RadSaratoga
2025-08-04 02:02:10 | DEBUG    | log                  | Object 'RadSaratoga' of type 'object/hosts' not found in cache (skipping live lookup during bulk operations)
2025-08-04 02:02:10 | DEBUG    | log                  | Cache has 0 unknown objects. Sample names: []
2025-08-04 02:02:10 | DEBUG    | log                  | No existing object/hosts 'RadSaratoga' found in cache - will attempt creation
2025-08-04 02:02:10 | ERROR    | log                  | Unknown object type for creation: object/hosts
2025-08-04 02:02:10 | WARNING  | log                  | 🔄 Attempt 3/10 failed for 'RadSaratoga': Failed to create object/hosts: RadSaratoga
2025-08-04 02:02:10 | INFO     | log                  | ⏳ Waiting 6s before retry 4/10 for RadSaratoga
2025-08-04 02:02:16 | DEBUG    | log                  | Object 'RadSaratoga' of type 'object/hosts' not found in cache (skipping live lookup during bulk operations)
2025-08-04 02:02:16 | DEBUG    | log                  | Cache has 0 unknown objects. Sample names: []
2025-08-04 02:02:16 | DEBUG    | log                  | No existing object/hosts 'RadSaratoga' found in cache - will attempt creation
2025-08-04 02:02:16 | ERROR    | log                  | Unknown object type for creation: object/hosts
2025-08-04 02:02:16 | WARNING  | log                  | 🔄 Attempt 4/10 failed for 'RadSaratoga': Failed to create object/hosts: RadSaratoga
2025-08-04 02:02:16 | INFO     | log                  | ⏳ Waiting 8s before retry 5/10 for RadSaratoga
2025-08-04 02:02:24 | DEBUG    | log                  | Object 'RadSaratoga' of type 'object/hosts' not found in cache (skipping live lookup during bulk operations)
2025-08-04 02:02:24 | DEBUG    | log                  | Cache has 0 unknown objects. Sample names: []
2025-08-04 02:02:24 | DEBUG    | log                  | No existing object/hosts 'RadSaratoga' found in cache - will attempt creation
2025-08-04 02:02:24 | ERROR    | log                  | Unknown object type for creation: object/hosts
2025-08-04 02:02:24 | WARNING  | log                  | 🔄 Attempt 5/10 failed for 'RadSaratoga': Failed to create object/hosts: RadSaratoga
2025-08-04 02:02:24 | INFO     | log                  | ⏳ Waiting 10s before retry 6/10 for RadSaratoga
2025-08-04 02:02:34 | DEBUG    | log                  | Object 'RadSaratoga' of type 'object/hosts' not found in cache (skipping live lookup during bulk operations)
2025-08-04 02:02:34 | DEBUG    | log                  | Cache has 0 unknown objects. Sample names: []
2025-08-04 02:02:34 | DEBUG    | log                  | No existing object/hosts 'RadSaratoga' found in cache - will attempt creation
2025-08-04 02:02:34 | ERROR    | log                  | Unknown object type for creation: object/hosts
2025-08-04 02:02:34 | WARNING  | log                  | 🔄 Attempt 6/10 failed for 'RadSaratoga': Failed to create object/hosts: RadSaratoga
2025-08-04 02:02:34 | INFO     | log                  | ⏳ Waiting 12s before retry 7/10 for RadSaratoga
2025-08-04 02:02:46 | DEBUG    | log                  | Object 'RadSaratoga' of type 'object/hosts' not found in cache (skipping live lookup during bulk operations)
2025-08-04 02:02:46 | DEBUG    | log                  | Cache has 0 unknown objects. Sample names: []
2025-08-04 02:02:46 | DEBUG    | log                  | No existing object/hosts 'RadSaratoga' found in cache - will attempt creation
2025-08-04 02:02:46 | ERROR    | log                  | Unknown object type for creation: object/hosts
2025-08-04 02:02:46 | WARNING  | log                  | 🔄 Attempt 7/10 failed for 'RadSaratoga': Failed to create object/hosts: RadSaratoga
2025-08-04 02:02:46 | INFO     | log                  | ⏳ Waiting 14s before retry 8/10 for RadSaratoga
2025-08-04 02:03:00 | DEBUG    | log                  | Object 'RadSaratoga' of type 'object/hosts' not found in cache (skipping live lookup during bulk operations)
2025-08-04 02:03:00 | DEBUG    | log                  | Cache has 0 unknown objects. Sample names: []
2025-08-04 02:03:00 | DEBUG    | log                  | No existing object/hosts 'RadSaratoga' found in cache - will attempt creation
2025-08-04 02:03:00 | ERROR    | log                  | Unknown object type for creation: object/hosts
2025-08-04 02:03:00 | WARNING  | log                  | 🔄 Attempt 8/10 failed for 'RadSaratoga': Failed to create object/hosts: RadSaratoga
2025-08-04 02:03:00 | INFO     | log                  | ⏳ Waiting 16s before retry 9/10 for RadSaratoga
2025-08-04 02:03:16 | DEBUG    | log                  | Object 'RadSaratoga' of type 'object/hosts' not found in cache (skipping live lookup during bulk operations)
2025-08-04 02:03:16 | DEBUG    | log                  | Cache has 0 unknown objects. Sample names: []
2025-08-04 02:03:16 | DEBUG    | log                  | No existing object/hosts 'RadSaratoga' found in cache - will attempt creation
2025-08-04 02:03:16 | ERROR    | log                  | Unknown object type for creation: object/hosts
2025-08-04 02:03:16 | WARNING  | log                  | 🔄 Attempt 9/10 failed for 'RadSaratoga': Failed to create object/hosts: RadSaratoga
2025-08-04 02:03:16 | INFO     | log                  | ⏳ Waiting 18s before retry 10/10 for RadSaratoga
2025-08-04 02:03:34 | DEBUG    | log                  | Object 'RadSaratoga' of type 'object/hosts' not found in cache (skipping live lookup during bulk operations)
2025-08-04 02:03:34 | DEBUG    | log                  | Cache has 0 unknown objects. Sample names: []
2025-08-04 02:03:34 | DEBUG    | log                  | No existing object/hosts 'RadSaratoga' found in cache - will attempt creation
2025-08-04 02:03:34 | ERROR    | log                  | Unknown object type for creation: object/hosts
2025-08-04 02:03:34 | WARNING  | log                  | 🔄 Attempt 10/10 failed for 'RadSaratoga': Failed to create object/hosts: RadSaratoga
2025-08-04 02:03:34 | ERROR    | log                  | ❌ CRITICAL: Failed to create object/hosts 'RadSaratoga' after 10 attempts - 100% synchronization cannot be guaranteed
2025-08-04 02:03:34 | DEBUG    | log                  | Cached lookup result for object/hosts:radamsmem
2025-08-04 02:03:34 | INFO     | log                  | Authenticating with FMC...
2025-08-04 02:04:04 | ERROR    | log                  | Authentication error: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x103810cd0>, 'Connection to ************* timed out. (connect timeout=30)'))
2025-08-04 02:04:04 | DEBUG    | log                  | 🎯 GUARANTEED SUCCESS MODE: Creating object/hosts 'RadAmsMem' with up to 10 retries
2025-08-04 02:04:04 | DEBUG    | log                  | Object 'RadAmsMem' of type 'object/hosts' not found in cache (skipping live lookup during bulk operations)
2025-08-04 02:04:04 | DEBUG    | log                  | Cache has 0 unknown objects. Sample names: []
2025-08-04 02:04:04 | DEBUG    | log                  | No existing object/hosts 'RadAmsMem' found in cache - will attempt creation
2025-08-04 02:04:04 | ERROR    | log                  | Unknown object type for creation: object/hosts
2025-08-04 02:04:04 | WARNING  | log                  | 🔄 Attempt 1/10 failed for 'RadAmsMem': Failed to create object/hosts: RadAmsMem
2025-08-04 02:04:04 | INFO     | log                  | ⏳ Waiting 2s before retry 2/10 for RadAmsMem
2025-08-04 02:04:06 | DEBUG    | log                  | Object 'RadAmsMem' of type 'object/hosts' not found in cache (skipping live lookup during bulk operations)
2025-08-04 02:04:06 | DEBUG    | log                  | Cache has 0 unknown objects. Sample names: []
2025-08-04 02:04:06 | DEBUG    | log                  | No existing object/hosts 'RadAmsMem' found in cache - will attempt creation
2025-08-04 02:04:06 | ERROR    | log                  | Unknown object type for creation: object/hosts
2025-08-04 02:04:06 | WARNING  | log                  | 🔄 Attempt 2/10 failed for 'RadAmsMem': Failed to create object/hosts: RadAmsMem
2025-08-04 02:04:06 | INFO     | log                  | ⏳ Waiting 4s before retry 3/10 for RadAmsMem
2025-08-04 02:04:10 | DEBUG    | log                  | Object 'RadAmsMem' of type 'object/hosts' not found in cache (skipping live lookup during bulk operations)
2025-08-04 02:04:10 | DEBUG    | log                  | Cache has 0 unknown objects. Sample names: []
2025-08-04 02:04:10 | DEBUG    | log                  | No existing object/hosts 'RadAmsMem' found in cache - will attempt creation
2025-08-04 02:04:10 | ERROR    | log                  | Unknown object type for creation: object/hosts
2025-08-04 02:04:10 | WARNING  | log                  | 🔄 Attempt 3/10 failed for 'RadAmsMem': Failed to create object/hosts: RadAmsMem
2025-08-04 02:04:10 | INFO     | log                  | ⏳ Waiting 6s before retry 4/10 for RadAmsMem
2025-08-04 02:04:16 | DEBUG    | log                  | Object 'RadAmsMem' of type 'object/hosts' not found in cache (skipping live lookup during bulk operations)
2025-08-04 02:04:16 | DEBUG    | log                  | Cache has 0 unknown objects. Sample names: []
2025-08-04 02:04:16 | DEBUG    | log                  | No existing object/hosts 'RadAmsMem' found in cache - will attempt creation
2025-08-04 02:04:16 | ERROR    | log                  | Unknown object type for creation: object/hosts
2025-08-04 02:04:16 | WARNING  | log                  | 🔄 Attempt 4/10 failed for 'RadAmsMem': Failed to create object/hosts: RadAmsMem
2025-08-04 02:04:16 | INFO     | log                  | ⏳ Waiting 8s before retry 5/10 for RadAmsMem
