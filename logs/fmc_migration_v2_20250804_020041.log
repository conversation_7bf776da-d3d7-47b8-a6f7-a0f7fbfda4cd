2025-08-04 02:00:41,298 | INFO | ================================================================================
2025-08-04 02:00:41,298 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 02:00:41,298 | INFO | Session ID: migration_1754298041
2025-08-04 02:00:41,298 | INFO | Connection Type: custom
2025-08-04 02:00:41,298 | INFO | 🔍 Connection Diagnostic:
2025-08-04 02:00:41,298 | INFO |    • FMC Object Type: <class 'fmc_api_executor.FMCAPIExecutor'>
2025-08-04 02:00:41,298 | INFO |    • fmcapi Available: True
2025-08-04 02:00:41,298 | INFO |    • Available Methods: _live_lookup_object_by_name, create_object_with_retry
2025-08-04 02:00:41,298 | INFO | 🔍 Connection diagnostic complete
2025-08-04 02:00:41,298 | INFO | ================================================================================
2025-08-04 02:00:41,298 | INFO | [LOAD] Loading migration configuration: fmc_migration_config.json
2025-08-04 02:00:41,301 | INFO | [DOC] Detected v1.0 config format (api_calls structure)
2025-08-04 02:00:41,301 | INFO | [INFO] Found 629 host objects in v1.0 format
2025-08-04 02:00:41,301 | INFO | [INFO] Found 63 network objects in v1.0 format
2025-08-04 02:00:41,301 | INFO | [INFO] Found 29 service objects in v1.0 format
2025-08-04 02:00:41,301 | INFO | [START] Starting Host Objects migration...
2025-08-04 02:00:41,301 | INFO | [INFO] Processing 629 hosts objects...
2025-08-04 02:00:41,301 | ERROR | Creation failed for RadSaratoga: Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'
2025-08-04 02:00:41,301 | ERROR | Creation failed for RadAmsMem: Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'
2025-08-04 02:00:41,301 | ERROR | Creation failed for RadStMarys: Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'
2025-08-04 02:00:41,301 | ERROR | Creation failed for RadSeton: Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'
2025-08-04 02:00:41,301 | ERROR | Creation failed for RadBellevue: Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'
2025-08-04 02:00:41,302 | INFO | [PROGRESS] Progress: 50/629 objects processed
2025-08-04 02:00:41,303 | INFO | [PROGRESS] Progress: 100/629 objects processed
2025-08-04 02:00:41,304 | INFO | [PROGRESS] Progress: 150/629 objects processed
2025-08-04 02:00:41,305 | INFO | [PROGRESS] Progress: 200/629 objects processed
2025-08-04 02:00:41,305 | INFO | [PROGRESS] Progress: 250/629 objects processed
2025-08-04 02:00:41,306 | INFO | [PROGRESS] Progress: 300/629 objects processed
2025-08-04 02:00:41,306 | INFO | [PROGRESS] Progress: 350/629 objects processed
2025-08-04 02:00:41,307 | INFO | [PROGRESS] Progress: 400/629 objects processed
2025-08-04 02:00:41,308 | INFO | [PROGRESS] Progress: 450/629 objects processed
2025-08-04 02:00:41,309 | INFO | [PROGRESS] Progress: 500/629 objects processed
2025-08-04 02:00:41,310 | INFO | [PROGRESS] Progress: 550/629 objects processed
2025-08-04 02:00:41,310 | INFO | [PROGRESS] Progress: 600/629 objects processed
2025-08-04 02:00:41,311 | INFO | [OK] Host Objects migration completed!
2025-08-04 02:00:41,311 | INFO | [INFO] Results: Created: 0, Updated: 0, Failed: 629, Skipped: 0
2025-08-04 02:00:41,311 | INFO | [TIME]  Duration: 0.01 seconds
2025-08-04 02:00:41,311 | INFO | [PROGRESS] Success Rate: 0.0%
2025-08-04 02:00:41,312 | INFO | [FILE] Checkpoint saved: migration_checkpoints/migration_1754298041_phase1_hosts.json
2025-08-04 02:00:41,312 | INFO | [START] Starting Network Objects migration...
2025-08-04 02:00:41,312 | INFO | [INFO] Processing 63 networks objects...
2025-08-04 02:00:41,312 | ERROR | Creation failed for TeleMedVT3: Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'
2025-08-04 02:00:41,312 | ERROR | Creation failed for TelemedVT4: Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'
2025-08-04 02:00:41,312 | ERROR | Creation failed for TelemedVT5: Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'
2025-08-04 02:00:41,313 | ERROR | Creation failed for TeleMedVT1: Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'
2025-08-04 02:00:41,313 | ERROR | Creation failed for Medent.VPN.net: Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'
2025-08-04 02:00:41,313 | INFO | [PROGRESS] Progress: 50/63 objects processed
2025-08-04 02:00:41,313 | INFO | [OK] Network Objects migration completed!
2025-08-04 02:00:41,314 | INFO | [INFO] Results: Created: 0, Updated: 0, Failed: 63, Skipped: 0
2025-08-04 02:00:41,314 | INFO | [TIME]  Duration: 0.00 seconds
2025-08-04 02:00:41,314 | INFO | [PROGRESS] Success Rate: 0.0%
2025-08-04 02:00:41,315 | INFO | [FILE] Checkpoint saved: migration_checkpoints/migration_1754298041_phase1_networks.json
2025-08-04 02:00:41,315 | INFO | [START] Starting Protocol Port Objects migration...
2025-08-04 02:00:41,315 | INFO | [INFO] Processing 29 services objects...
2025-08-04 02:00:41,315 | ERROR | Creation failed for obj-tcp-eq-80: Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'
2025-08-04 02:00:41,315 | ERROR | Creation failed for obj-tcp-eq-15002: Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'
2025-08-04 02:00:41,315 | ERROR | Creation failed for obj-tcp-eq-15331: Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'
2025-08-04 02:00:41,315 | ERROR | Creation failed for obj-tcp-eq-3389: Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'
2025-08-04 02:00:41,315 | ERROR | Creation failed for obj-tcp-eq-2222: Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'
2025-08-04 02:00:41,316 | INFO | [OK] Protocol Port Objects migration completed!
2025-08-04 02:00:41,316 | INFO | [INFO] Results: Created: 0, Updated: 0, Failed: 29, Skipped: 0
2025-08-04 02:00:41,316 | INFO | [TIME]  Duration: 0.00 seconds
2025-08-04 02:00:41,316 | INFO | [PROGRESS] Success Rate: 0.0%
2025-08-04 02:00:41,316 | INFO | [FILE] Checkpoint saved: migration_checkpoints/migration_1754298041_phase1_services.json
2025-08-04 02:00:41,316 | INFO | ================================================================================
2025-08-04 02:00:41,316 | INFO | MIGRATION SUMMARY
2025-08-04 02:00:41,316 | INFO | ================================================================================
2025-08-04 02:00:41,316 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 02:00:41,316 | INFO |    • Total Objects: 721
2025-08-04 02:00:41,316 | INFO |    • Created: 0
2025-08-04 02:00:41,316 | INFO |    • Updated: 0
2025-08-04 02:00:41,316 | INFO |    • Failed: 721
2025-08-04 02:00:41,316 | INFO |    • Skipped: 0
2025-08-04 02:00:41,316 | INFO |    • Success Rate: 0.0%
2025-08-04 02:00:41,316 | INFO | ================================================================================
2025-08-04 02:00:41,317 | INFO | [FILE] Summary saved: migration_summary_migration_1754298041.json
2025-08-04 02:00:41,317 | WARNING | [WARN]  Migration completed with 721 failures
