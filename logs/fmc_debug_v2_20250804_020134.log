2025-08-04 02:01:34,790 | INFO | ================================================================================
2025-08-04 02:01:34,790 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 02:01:34,790 | INFO | Session ID: migration_1754298094
2025-08-04 02:01:34,791 | INFO | Connection Type: custom
2025-08-04 02:01:34,791 | INFO | 🔍 Connection Diagnostic:
2025-08-04 02:01:34,791 | INFO |    • FMC Object Type: <class 'fmc_api_executor.FMCAPIExecutor'>
2025-08-04 02:01:34,791 | INFO |    • fmcapi Available: True
2025-08-04 02:01:34,791 | INFO |    • Available Methods: _live_lookup_object_by_name, create_object_with_retry
2025-08-04 02:01:34,791 | INFO | 🔍 Connection diagnostic complete
2025-08-04 02:01:34,791 | INFO | ================================================================================
2025-08-04 02:01:34,791 | INFO | [LOAD] Loading migration configuration: fmc_migration_config.json
2025-08-04 02:01:34,793 | INFO | [DOC] Detected v1.0 config format (api_calls structure)
2025-08-04 02:01:34,793 | INFO | [INFO] Found 629 host objects in v1.0 format
2025-08-04 02:01:34,794 | INFO | [INFO] Found 63 network objects in v1.0 format
2025-08-04 02:01:34,794 | INFO | [INFO] Found 29 service objects in v1.0 format
2025-08-04 02:01:34,794 | INFO | [START] Starting Host Objects migration...
2025-08-04 02:01:34,794 | INFO | [INFO] Processing 629 hosts objects...
2025-08-04 02:01:34,794 | DEBUG | Processing hosts 1: RadSaratoga
2025-08-04 02:01:34,794 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 02:01:34,794 | DEBUG | Object data: {'name': 'RadSaratoga', 'type': 'Host', 'value': '***********', 'description': 'Migrated from ASA - ', 'overridable': False}
2025-08-04 02:01:34,794 | DEBUG | GET result for RadSaratoga: success=False, message='Custom lookup failed: No result'
2025-08-04 02:03:34,841 | DEBUG | POST result for RadSaratoga: success=False, message='Custom creation failed: ❌ CRITICAL: Failed to create object/hosts 'RadSaratoga' after 10 attempts - 100% synchronization cannot be guaranteed'
2025-08-04 02:03:34,841 | ERROR | Creation failed for RadSaratoga: Custom creation failed: ❌ CRITICAL: Failed to create object/hosts 'RadSaratoga' after 10 attempts - 100% synchronization cannot be guaranteed
2025-08-04 02:03:34,841 | DEBUG | Processing hosts 2: RadAmsMem
2025-08-04 02:03:34,841 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 02:03:34,841 | DEBUG | Object data: {'name': 'RadAmsMem', 'type': 'Host', 'value': '***********', 'description': 'Migrated from ASA - ', 'overridable': False}
2025-08-04 02:03:34,842 | DEBUG | GET result for RadAmsMem: success=False, message='Custom lookup failed: No result'
